import { Response } from "express";
import { TransactionService } from "../services/transactionService";
import { ApprovalService } from "../services/approvalService";
import { OptimizedPDFService } from "../services/optimizedPdfService";
import { OptimizedZipService } from "../services/optimizedZipService";
import { CSVService } from "../services/csvService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";
import { LOAN_TYPE_CONFIGS } from "../types/loanTypes";
import { LoanEligibilityService } from "../services/loanEligibilityService";
import prisma from "../db/db";
import { TransactionStatus } from "@prisma/client";

export class TransactionController {
  static createTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.id;

      const result = await TransactionService.createTransaction(userId);

      ResponseHandler.success(
        res,
        "Transaction created successfully",
        result,
        201
      );
    }
  );

  static updatePersonalInfo = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;
      const data = req.body;

      console.log("Controller updatePersonalInfo called:", {
        transactionId,
        userId,
        userExists: !!req.user,
        bodyKeys: Object.keys(data || {}),
      });

      if (!userId) {
        console.error("No user ID found in request");
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId) {
        console.error("No transaction ID provided");
        return ResponseHandler.error(
          res,
          "Transaction ID is required",
          undefined,
          400
        );
      }

      await TransactionService.updatePersonalInfo(transactionId, data, userId);

      ResponseHandler.success(res, "Personal information updated successfully");
    }
  );

  static updateNextOfKin = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateNextOfKin(transactionId, data, userId);

      ResponseHandler.success(
        res,
        "Next of kin information updated successfully"
      );
    }
  );

  static updateLoanInfo = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateLoanInfo(transactionId, data, userId);

      ResponseHandler.success(res, "Loan information updated successfully");
    }
  );

  static updateDisbursement = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.updateDisbursement(transactionId, data, userId);

      ResponseHandler.success(
        res,
        "Disbursement information updated successfully"
      );
    }
  );

  static submitTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;

      console.log("Submit transaction request:", { transactionId, userId });

      try {
        await TransactionService.submitTransaction(transactionId, userId);
        ResponseHandler.success(res, "Transaction submitted successfully");
      } catch (error: any) {
        console.error("Submit transaction error:", {
          transactionId,
          userId,
          error: error.message,
          isOperational: error.isOperational,
          statusCode: error.statusCode,
        });
        throw error; // Re-throw to let error handler handle it
      }
    }
  );

  static getTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await TransactionService.getTransaction(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction retrieved successfully",
        transaction
      );
    }
  );

  static getTransactions = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const filters = {
        status: req.query.status as any,
        stage: req.query.stage as any,
        createdById: req.query.createdById as string,
        dateFrom: req.query.dateFrom
          ? new Date(req.query.dateFrom as string)
          : undefined,
        dateTo: req.query.dateTo
          ? new Date(req.query.dateTo as string)
          : undefined,
        search: req.query.search as string,
      };

      const pagination = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        sortBy: (req.query.sortBy as string) || "createdAt",
        sortOrder: (req.query.sortOrder as "asc" | "desc") || "desc",
      };

      const result = await TransactionService.getTransactions(
        filters,
        pagination,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transactions retrieved successfully",
        result
      );
    }
  );

  static deleteTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;

      await TransactionService.deleteTransaction(transactionId, userId);

      ResponseHandler.success(res, "Transaction deleted successfully");
    }
  );

  static setLoanType = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const data = req.body;

      await TransactionService.setLoanType(transactionId, data, userId);

      ResponseHandler.success(res, "Loan type set successfully");
    }
  );

  static getTransactionPreview = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Transaction preview retrieved successfully",
        transaction
      );
    }
  );

  static downloadTransactionPDF = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log(
        `📄 PDF download request for transaction ${transactionId} by user ${userId}`
      );

      try {
        // Get transaction data (this may be cached)
        const transaction = await TransactionService.getTransactionPreview(
          transactionId,
          userId,
          userRole
        );

        if (!transaction) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        // Generate PDF (with caching)
        const pdfBuffer = await OptimizedPDFService.generateTransactionPDF(
          transaction
        );

        // Set optimized response headers
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="transaction-${transaction.transactionId}.pdf"`
        );
        res.setHeader("Content-Length", pdfBuffer.length);

        // Add caching headers for client-side caching
        res.setHeader("Cache-Control", "private, max-age=3600"); // 1 hour
        res.setHeader(
          "ETag",
          `"pdf-${transaction.id}-${transaction.updatedAt}"`
        );

        // Add performance headers
        res.setHeader("X-Generated-By", "OptimizedPDFService");
        res.setHeader("X-Cache-Status", "optimized");

        console.log(
          `✅ PDF sent for transaction ${transactionId} (${pdfBuffer.length} bytes)`
        );
        res.send(pdfBuffer);
      } catch (error) {
        console.error(
          `❌ PDF generation failed for transaction ${transactionId}:`,
          error
        );
        return ResponseHandler.error(
          res,
          "Failed to generate PDF",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );

  static downloadTransactionCSV = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log("CSV export request:", { transactionId, userId, userRole });

      // Get transaction data
      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      // Generate CSV content
      const csvContent = CSVService.generateTransactionCSV(transaction);
      const filename = CSVService.generateCSVFilename(
        transaction.transactionId
      );

      // Set response headers for CSV download
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );
      res.setHeader("Content-Length", Buffer.byteLength(csvContent, "utf8"));

      console.log("CSV export successful:", {
        transactionId,
        filename,
        contentLength: Buffer.byteLength(csvContent, "utf8"),
      });

      res.send(csvContent);
    }
  );

  static getLoanTypes = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const loanTypes = Object.values(LOAN_TYPE_CONFIGS);

      ResponseHandler.success(
        res,
        "Loan types retrieved successfully",
        loanTypes
      );
    }
  );

  static downloadAll = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log(
        `📦 ZIP download request for transaction ${transactionId} by user ${userId}`
      );

      try {
        // Validate transaction and check access (cached)
        const validation = await OptimizedZipService.validateTransactionForZip(
          transactionId,
          userId,
          userRole
        );

        if (!validation.canAccess) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        if (!validation.hasContent) {
          return ResponseHandler.error(
            res,
            "No content available for download",
            undefined,
            404
          );
        }

        // Get estimated file size for logging and headers
        const estimatedSize = await OptimizedZipService.getEstimatedZipSize(
          transactionId,
          userId,
          userRole
        );

        console.log(
          `📦 Creating optimized ZIP for transaction ${transactionId}, estimated size: ${estimatedSize} bytes`
        );

        // Create ZIP archive (with caching and parallel processing)
        const { zipBuffer, fileName } =
          await OptimizedZipService.createTransactionZip(
            transactionId,
            userId,
            userRole
          );

        // Set optimized response headers
        res.setHeader("Content-Type", "application/zip");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${fileName}"`
        );
        res.setHeader("Content-Length", zipBuffer.length);

        // Add performance headers
        res.setHeader("X-Generated-By", "OptimizedZipService");
        res.setHeader("X-Estimated-Size", estimatedSize.toString());
        res.setHeader("X-Actual-Size", zipBuffer.length.toString());
        res.setHeader(
          "X-Compression-Ratio",
          (((estimatedSize - zipBuffer.length) / estimatedSize) * 100).toFixed(
            2
          ) + "%"
        );

        // Add caching headers
        res.setHeader("Cache-Control", "private, max-age=1800"); // 30 minutes
        res.setHeader("ETag", `"zip-${transactionId}-${Date.now()}"`);

        console.log(
          `✅ ZIP sent for transaction ${transactionId} (${zipBuffer.length} bytes, ${fileName})`
        );

        res.send(zipBuffer);
      } catch (error) {
        console.error(
          `❌ ZIP creation failed for transaction ${transactionId}:`,
          error
        );
        return ResponseHandler.error(
          res,
          "Failed to create ZIP archive",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );

  static getApprovalHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      // Check if user has permission to view this transaction's approval history
      // For now, allow all authenticated users to view approval history
      // You can add more specific authorization logic here if needed

      const approvalHistory = await ApprovalService.getDetailedApprovalHistory(
        transactionId
      );

      ResponseHandler.success(
        res,
        "Approval history retrieved successfully",
        approvalHistory
      );
    }
  );

  static checkLoanEligibility = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { bvn } = req.body;

      if (!bvn) {
        return ResponseHandler.error(res, "BVN is required", undefined, 400);
      }

      if (!/^\d{11}$/.test(bvn)) {
        return ResponseHandler.error(
          res,
          "BVN must be exactly 11 digits",
          undefined,
          400
        );
      }

      const eligibilityResult =
        await LoanEligibilityService.checkLoanEligibilityByBVN(bvn);

      ResponseHandler.success(
        res,
        eligibilityResult.isEligible
          ? "You are eligible for a new loan"
          : "You are not eligible for a new loan at this time",
        eligibilityResult
      );
    }
  );

  static getLoanHistory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { bvn } = req.params;

      if (!bvn) {
        return ResponseHandler.error(res, "BVN is required", undefined, 400);
      }

      if (!/^\d{11}$/.test(bvn)) {
        return ResponseHandler.error(
          res,
          "BVN must be exactly 11 digits",
          undefined,
          400
        );
      }

      const loanHistory = await LoanEligibilityService.getLoanHistoryByBVN(bvn);

      ResponseHandler.success(
        res,
        "Loan history retrieved successfully",
        loanHistory
      );
    }
  );

  static markLoanCompleted = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { completionReason } = req.body;

      await LoanEligibilityService.markLoanAsCompleted(
        transactionId,
        completionReason
      );

      ResponseHandler.success(res, "Loan marked as completed successfully");
    }
  );

  static markLoanRepaid = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const { repaymentMethod, repaymentReference, notes } = req.body;

      await LoanEligibilityService.markLoanAsRepaid(transactionId, {
        repaymentMethod,
        repaymentReference,
        notes,
      });

      ResponseHandler.success(res, "Loan marked as repaid successfully");
    }
  );

  static getTransactionStatus = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;

      console.log("Getting transaction status:", { transactionId, userId });

      if (!userId) {
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId || transactionId.length !== 24) {
        return ResponseHandler.error(
          res,
          "Invalid transaction ID format",
          undefined,
          400
        );
      }

      const transaction = await prisma.transaction.findFirst({
        where: { id: transactionId, createdById: userId },
        select: {
          status: true,
          transactionId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!transaction) {
        console.log("Transaction not found for status check:", {
          transactionId,
          userId,
        });
        return ResponseHandler.error(
          res,
          "Transaction not found",
          undefined,
          404
        );
      }

      console.log("Transaction status found:", transaction);

      ResponseHandler.success(res, "Transaction status retrieved", {
        status: transaction.status,
        transactionId: transaction.transactionId,
        canEdit: transaction.status === TransactionStatus.DRAFT,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
      });
    }
  );

  static validateTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user?.id;

      console.log("Validating transaction:", { transactionId, userId });

      if (!userId) {
        return ResponseHandler.error(
          res,
          "User not authenticated",
          undefined,
          401
        );
      }

      if (!transactionId || transactionId.length !== 24) {
        return ResponseHandler.error(
          res,
          "Invalid transaction ID format",
          undefined,
          400
        );
      }

      const transaction = await prisma.transaction.findFirst({
        where: { id: transactionId, createdById: userId },
        include: { documents: true },
      });

      if (!transaction) {
        return ResponseHandler.error(
          res,
          "Transaction not found",
          undefined,
          404
        );
      }

      // Get validation details using the new method
      const validationResult = await TransactionService.getValidationDetails(
        transaction
      );

      ResponseHandler.success(res, "Transaction validation completed", {
        isComplete: validationResult.isComplete,
        missingFields: validationResult.missingFields,
        missingDocuments: validationResult.missingDocuments,
        canSubmit:
          validationResult.isComplete &&
          (transaction.status === TransactionStatus.DRAFT ||
            transaction.status === TransactionStatus.SENT_BACK),
        status: transaction.status,
        transactionId: transaction.transactionId,
      });
    }
  );
}
