import prisma from "../db/db";
import { UserRole, TransactionStatus } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";
import { TransactionService } from "./transactionService";

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  email?: string;
  role?: UserRole;
  monthlyTarget?: number;
  isActive?: boolean;
}

export class UserService {
  /**
   * Delete a user with safety checks
   */
  static async deleteUser(userId: string, adminId: string): Promise<void> {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        createdTransactions: {
          where: {
            status: {
              in: [
                TransactionStatus.DRAFT,
                TransactionStatus.SUBMITTED,
                TransactionStatus.IN_PROGRESS,
                TransactionStatus.APPROVED,
                TransactionStatus.DISBURSED,
              ],
            },
          },
        },
        approvals: {
          where: {
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        },
      },
    });

    if (!user) {
      throw new OperationalError(
        "The user you're trying to delete could not be found. Please verify the user ID and try again.",
        404
      );
    }

    // Prevent deletion of users with active transactions
    if (user.createdTransactions.length > 0) {
      throw new OperationalError(
        `Cannot delete user '${user.firstName} ${user.lastName}' because they have ${user.createdTransactions.length} active transaction(s). Please ensure all transactions are completed, cancelled, or reassigned to another user before attempting deletion.`,
        400
      );
    }

    // Prevent deletion of users with recent approvals
    if (user.approvals.length > 0) {
      throw new OperationalError(
        `Cannot delete user '${user.firstName} ${user.lastName}' because they have ${user.approvals.length} recent approval activities in the last 30 days. Please wait for all pending approvals to be resolved before attempting deletion.`,
        400
      );
    }

    // Prevent deletion of the last Super Admin
    if (user.role === UserRole.SUPER_ADMIN) {
      const superAdminCount = await prisma.user.count({
        where: {
          role: UserRole.SUPER_ADMIN,
          isActive: true,
        },
      });

      if (superAdminCount <= 1) {
        throw new OperationalError(
          "Cannot delete the last Super Admin user. Create another Super Admin first.",
          400
        );
      }
    }

    // Prevent self-deletion
    if (userId === adminId) {
      throw new OperationalError(
        "You cannot delete your own account. Ask another Super Admin to perform this action.",
        400
      );
    }

    // Perform the deletion
    await prisma.user.delete({
      where: { id: userId },
    });

    console.log("User deletion completed:", {
      deletedUserId: userId,
      deletedUserEmail: user.email,
      deletedUserRole: user.role,
      deletedBy: adminId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Update user details with validation
   */
  static async updateUser(
    userId: string,
    updateData: UpdateUserData,
    updatedBy: string
  ): Promise<any> {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new OperationalError("User not found", 404);
    }

    // Validate email uniqueness if email is being updated
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await prisma.user.findFirst({
        where: {
          email: updateData.email,
          id: { not: userId },
        },
      });

      if (emailExists) {
        throw new OperationalError(
          `The email address '${updateData.email}' is already in use by another account. Please choose a different email address.`,
          409
        );
      }
    }

    // Validate role if being updated
    if (updateData.role && !Object.values(UserRole).includes(updateData.role)) {
      throw new OperationalError(
        `The role '${
          updateData.role
        }' is not valid. Valid roles are: ${Object.values(UserRole).join(
          ", "
        )}.`,
        400
      );
    }

    // Prevent deactivating the last Super Admin
    if (
      updateData.isActive === false &&
      existingUser.role === UserRole.SUPER_ADMIN
    ) {
      const activeSuperAdminCount = await prisma.user.count({
        where: {
          role: UserRole.SUPER_ADMIN,
          isActive: true,
          id: { not: userId },
        },
      });

      if (activeSuperAdminCount === 0) {
        throw new OperationalError(
          "Cannot deactivate the last active Super Admin",
          400
        );
      }
    }

    // Prepare update data
    const updatePayload: any = {};

    if (updateData.firstName !== undefined) {
      updatePayload.firstName = updateData.firstName;
    }
    if (updateData.lastName !== undefined) {
      updatePayload.lastName = updateData.lastName;
    }
    if (updateData.email !== undefined) {
      updatePayload.email = updateData.email;
    }
    if (updateData.role !== undefined) {
      updatePayload.role = updateData.role;
    }
    if (updateData.monthlyTarget !== undefined) {
      updatePayload.monthlyTarget = updateData.monthlyTarget;
    }
    if (updateData.isActive !== undefined) {
      updatePayload.isActive = updateData.isActive;
    }

    // Always update the updatedAt timestamp
    updatePayload.updatedAt = new Date();

    // Perform the update
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updatePayload,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        monthlyTarget: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log("User update completed:", {
      updatedUserId: userId,
      updatedFields: Object.keys(updatePayload),
      updatedBy,
      timestamp: new Date().toISOString(),
    });

    return updatedUser;
  }

  /**
   * Get user by ID with safety checks
   */
  static async getUserById(userId: string): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        monthlyTarget: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new OperationalError(
        "The user you're trying to update could not be found. Please verify the user ID and try again.",
        404
      );
    }

    return user;
  }

  /**
   * Check if user has active transactions or pending approvals
   */
  static async checkUserDependencies(userId: string): Promise<{
    hasActiveTransactions: boolean;
    hasPendingApprovals: boolean;
    activeTransactionCount: number;
    pendingApprovalCount: number;
  }> {
    const [activeTransactions, pendingApprovals] = await Promise.all([
      prisma.transaction.count({
        where: {
          createdById: userId,
          status: {
            in: [
              TransactionStatus.DRAFT,
              TransactionStatus.SUBMITTED,
              TransactionStatus.IN_PROGRESS,
              TransactionStatus.APPROVED,
              TransactionStatus.DISBURSED,
            ],
          },
        },
      }),
      prisma.transactionApproval.count({
        where: {
          approverId: userId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return {
      hasActiveTransactions: activeTransactions > 0,
      hasPendingApprovals: pendingApprovals > 0,
      activeTransactionCount: activeTransactions,
      pendingApprovalCount: pendingApprovals,
    };
  }

  /**
   * Transfer all transferable transactions from one user to another
   * This is used when deactivating or deleting account officers
   */
  static async transferUserTransactions(
    fromUserId: string,
    toUserId: string,
    transferredBy: string,
    transferReason?: string
  ): Promise<{ transferredCount: number; transactionIds: string[] }> {
    return await TransactionService.bulkTransferTransactions(
      fromUserId,
      toUserId,
      transferredBy,
      transferReason
    );
  }

  /**
   * Get account officers who can receive transferred transactions
   */
  static async getAvailableAccountOfficers(
    excludeUserId?: string
  ): Promise<any[]> {
    const whereClause: any = {
      role: UserRole.ACCOUNT_OFFICER,
      isActive: true,
    };

    if (excludeUserId) {
      whereClause.id = { not: excludeUserId };
    }

    const accountOfficers = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        monthlyTarget: true,
        _count: {
          select: {
            createdTransactions: {
              where: {
                status: {
                  in: [
                    TransactionStatus.DRAFT,
                    TransactionStatus.SENT_BACK,
                    TransactionStatus.SUBMITTED,
                    TransactionStatus.IN_PROGRESS,
                  ],
                },
              },
            },
          },
        },
      },
      orderBy: {
        firstName: "asc",
      },
    });

    return accountOfficers.map((officer) => ({
      id: officer.id,
      name: `${officer.firstName} ${officer.lastName}`,
      email: officer.email,
      monthlyTarget: officer.monthlyTarget,
      activeTransactionCount: officer._count.createdTransactions,
    }));
  }

  /**
   * Enhanced delete user method with transaction transfer option
   */
  static async deleteUserWithTransferOption(
    userId: string,
    adminId: string,
    transferToUserId?: string,
    transferReason?: string
  ): Promise<{ deletedUser: any; transferResult?: any }> {
    // Check if user exists and get their details
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        createdTransactions: {
          where: {
            status: {
              in: [
                TransactionStatus.DRAFT,
                TransactionStatus.SENT_BACK,
                TransactionStatus.APPROVED,
              ],
            },
          },
        },
      },
    });

    if (!user) {
      throw new OperationalError(
        "The user you're trying to delete could not be found. Please verify the user ID and try again.",
        404
      );
    }

    // If user has transferable transactions and no transfer target is provided, throw error
    if (user.createdTransactions.length > 0 && !transferToUserId) {
      throw new OperationalError(
        `Cannot delete user '${user.firstName} ${user.lastName}' because they have ${user.createdTransactions.length} transferable transaction(s). Please specify a target user to transfer these transactions to, or transfer them manually before deletion.`,
        400
      );
    }

    let transferResult;

    // Transfer transactions if needed
    if (user.createdTransactions.length > 0 && transferToUserId) {
      transferResult = await this.transferUserTransactions(
        userId,
        transferToUserId,
        adminId,
        transferReason ||
          `User deletion - transferring transactions from ${user.firstName} ${user.lastName}`
      );
    }

    // Perform standard deletion checks
    await this.deleteUser(userId, adminId);

    return {
      deletedUser: {
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
        role: user.role,
      },
      transferResult,
    };
  }
}
